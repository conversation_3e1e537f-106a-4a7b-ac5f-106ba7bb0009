<template>
  <VbenPageLayout @search-collapse-change="handleSearchCollapseChange">
    <!-- 搜索表单插槽 -->
    <template #search>
      <YdSearchForm
        v-model="searchForm"
        :fields="searchFields"
        :show-collapse-button="true"
        :default-collapsed="false"
        :collapsed-rows="1"
        @search="handleSearch"
        @reset="handleReset"
        @collapse-change="handleSearchCollapseChange"
      />
    </template>

    <!-- 数据表格 -->
    <YdDataTable
      :show-selection="true"
      :columns="columns"
      :data="tableData"
      :loading="loading"
      :total="total"
      :current-page="currentPage"
      :page-size="pageSize"
      :auto-height="true"
      highlight-current-row
      :row-clickable="true"
      @page-change="handlePageChange"
      @size-change="handleSizeChange"
      @sort-change="handleSortChange"
      @row-click="handleRowClick"
    />
  </VbenPageLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import VbenPageLayout from '@/components/common/VbenPageLayout.vue'
import YdSearchForm from '@/components/common/YdSearchForm.vue'
import YdDataTable from '@/components/common/YdDataTable.vue'
import { ElMessage } from 'element-plus'
defineOptions({
  name: 'BidAnnouncement',
})

// 搜索表单数据
const searchForm = ref({})

// 搜索字段配置
const searchFields = [
  {
    type: 'text',
    name: 'announcementNumber',
    label: '公告编号',
    placeholder: '请输入公告编号',
  },
  {
    type: 'text',
    name: 'title',
    label: '公告标题',
    placeholder: '请输入公告标题',
  },
  {
    type: 'select',
    name: 'type',
    label: '公告类型',
    placeholder: '请选择公告类型',
    options: [
      { label: '全部', value: '' },
      { label: '招标公告', value: 'tender' },
      { label: '中标公告', value: 'award' },
      { label: '变更公告', value: 'change' },
      { label: '废标公告', value: 'cancel' },
    ],
  },
  {
    type: 'select',
    name: 'procurementMethod',
    label: '采购方式',
    placeholder: '请选择采购方式',
    options: [
      { label: '全部', value: '' },
      { label: '公开招标', value: 'open' },
      { label: '邀请招标', value: 'invite' },
      { label: '竞争性谈判', value: 'negotiate' },
      { label: '单一来源', value: 'single' },
      { label: '询价', value: 'inquiry' },
    ],
  },
  {
    type: 'daterange',
    name: 'publishTime',
    label: '发布时间',
  },
  {
    type: 'select',
    name: 'status',
    label: '状态',
    placeholder: '请选择状态',
    options: [
      { label: '全部', value: '' },
      { label: '有效', value: '1' },
      { label: '无效', value: '0' },
    ],
  },
]

// 表格列配置
const columns = [
  { dataIndex: 'serialNumber', title: '序号', width: 80 },
  { dataIndex: 'announcementNumber', title: '公告编号', width: 120 },
  { dataIndex: 'title', title: '公告标题', minWidth: 200 },
  { dataIndex: 'purchaser', title: '采购人', width: 150 },
  { dataIndex: 'type', title: '公告类型', width: 100 },
  {
    dataIndex: 'publishTime',
    title: '发布时间',
    width: 150,
    sortable: 'custom',
  },
  {
    dataIndex: 'registrationDeadline',
    title: '报名截止时间',
    width: 150,
    sortable: 'custom',
  },
  { dataIndex: 'status', title: '状态', width: 80 },
]

// 表格数据
const tableData = ref([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 排序状态
const sortInfo = ref({
  prop: '',
  order: '',
})

// 路由实例
const router = useRouter()

// 模拟数据
const mockData = [
  {
    id: 1,
    announcementNumber: '*********',
    serialNumber: 1,
    title: '某医院医疗设备采购项目招标公告',
    purchaser: 'XXX医院',
    type: '招标公告',
    publishTime: '2023-06-07 15:34:09',
    registrationDeadline: '2023-11-07 18:00:00',
    status: '有效',
  },
  {
    id: 2,
    serialNumber: 2,
    announcementNumber: '*********',
    title: '学校教学设备采购项目招标公告',
    purchaser: 'XXX学校',
    type: '招标公告',
    publishTime: '2023-06-07 15:34:09',
    registrationDeadline: '2023-10-30 18:00:00',
    status: '已结束',
  },
  {
    id: 3,
    serialNumber: 3,
    announcementNumber: '*********',
    title: '办公设备采购项目招标公告',
    purchaser: 'XXX政府部门',
    type: '招标公告',
    publishTime: '2023-06-05 10:20:00',
    registrationDeadline: '2023-12-15 17:00:00',
    status: '有效',
  },
  {
    id: 4,
    serialNumber: 4,
    announcementNumber: '*********',
    title: '网络设备采购项目招标公告',
    purchaser: 'XXX企业',
    type: '招标公告',
    publishTime: '2023-06-10 09:15:30',
    registrationDeadline: '2023-09-20 16:30:00',
    status: '有效',
  },
]

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadData()
}

// 重置
const handleReset = () => {
  currentPage.value = 1
  loadData()
}

// 分页变化
const handlePageChange = (page) => {
  currentPage.value = page
  loadData()
}

// 页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadData()
}

// 排序变化
const handleSortChange = ({ prop, order }) => {
  sortInfo.value.prop = prop
  sortInfo.value.order = order
  currentPage.value = 1
  loadData()
}

// 行点击
const handleRowClick = (row) => {
  if (row && row.id) {
    router.push(`/announcement/${row.id}`)
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 500))

    let data = [...mockData]

    // 应用排序
    if (sortInfo.value.prop && sortInfo.value.order) {
      data.sort((a, b) => {
        const aValue = a[sortInfo.value.prop]
        const bValue = b[sortInfo.value.prop]

        // 日期排序
        if (
          sortInfo.value.prop === 'publishTime' ||
          sortInfo.value.prop === 'registrationDeadline'
        ) {
          const dateA = new Date(aValue)
          const dateB = new Date(bValue)

          if (sortInfo.value.order === 'ascending') {
            return dateA - dateB
          } else {
            return dateB - dateA
          }
        }

        // 其他类型排序
        if (sortInfo.value.order === 'ascending') {
          return aValue > bValue ? 1 : -1
        } else {
          return aValue < bValue ? 1 : -1
        }
      })
    }

    tableData.value = data
    total.value = data.length
  } catch (error) {
    ElMessage.error(error)
  } finally {
    loading.value = false
  }
}

// 处理搜索区域展开收起变化
const handleSearchCollapseChange = (collapsed) => {
  console.log('搜索区域收起状态:', collapsed)
  // 这里可以添加其他需要在搜索区域展开收起时执行的逻辑
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
/* 使用 VbenPageLayout 后，不需要额外的样式 */
</style>
