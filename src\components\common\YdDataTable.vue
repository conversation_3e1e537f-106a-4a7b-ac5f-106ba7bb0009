<!-- eslint-disable vue/no-v-html -->
<!-- eslint-disable vue/no-unused-vars -->
<template>
  <div ref="tableContainerRef" class="data-table-container">
    <div class="table-wrapper">
      <el-table
        v-bind="$attrs"
        :data="currentPageData"
        :loading="loading"
        :row-style="rowStyle"
        :height="actualTableHeight"
        :max-height="maxHeight"
        :highlight-current-row="highlightCurrentRow"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
        @sort-change="handleSortChange"
      >
        <!-- 选择列 -->
        <el-table-column v-if="showSelection" type="selection" width="55" />

        <!-- 动态生成列 -->
        <el-table-column
          v-for="column in columns"
          :key="column.key || column.dataIndex"
          :prop="column.dataIndex"
          :label="column.title"
          :width="column.width"
          :min-width="column.minWidth"
          :fixed="column.fixed"
          :sortable="column.sortable"
          :align="column.align || 'left'"
        >
          <template #default="{ row, column: col, $index }">
            <!-- 自定义渲染插槽 -->
            <slot
              v-if="column.slot"
              :name="column.slot"
              :text="getColumnValue(row, column.dataIndex)"
              :record="row"
              :index="$index"
              :column="column"
            >
              {{ getColumnValue(row, column.dataIndex) }}
            </slot>

            <!-- 标签类型 -->
            <el-tag
              v-else-if="column.type === 'tag'"
              :type="
                getTagType(getColumnValue(row, column.dataIndex), column.tagMap)
              "
              size="small"
            >
              {{
                getTagText(getColumnValue(row, column.dataIndex), column.tagMap)
              }}
            </el-tag>

            <!-- 链接类型 -->
            <el-link
              v-else-if="column.type === 'link'"
              type="primary"
              @click="handleLinkClick(row, column)"
            >
              <span
                v-html="highlightText(getColumnValue(row, column.dataIndex))"
              ></span>
            </el-link>

            <!-- 高亮文本 -->
            <span
              v-else-if="column.highlight"
              v-html="highlightText(getColumnValue(row, column.dataIndex))"
            ></span>

            <!-- 默认文本 -->
            <span v-else>
              {{ formatValue(getColumnValue(row, column.dataIndex), column) }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 集成分页组件 -->
    <div v-if="showPagination && pagination.total > 0" class="table-pagination">
      <div class="pagination-controls">
        <el-config-provider :locale="zhCn">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="pageSizes"
            :total="pagination.total"
            :layout="'total, ->, sizes, prev, pager, next, jumper'"
            :background="true"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-config-provider>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  computed,
  reactive,
  watch,
  ref,
  onMounted,
  onUnmounted,
  nextTick,
} from 'vue'
import { ElConfigProvider } from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

zhCn.el.pagination.total = '共 {total} 项数据'
zhCn.el.pagination.goto = '跳至'

defineOptions({
  name: 'YdDataTable',
})

// Props
const props = defineProps({
  columns: {
    type: Array,
    required: true,
  },
  data: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  showSelection: {
    type: Boolean,
    default: false,
  },
  highlightKeyword: {
    type: String,
    default: '',
  },
  rowClickable: {
    type: Boolean,
    default: false,
  },
  tableHeight: {
    type: [String, Number],
    default: 'auto', // 默认自适应高度
  },
  maxHeight: {
    type: [String, Number],
    default: undefined,
  },
  // 是否自适应容器高度
  autoHeight: {
    type: Boolean,
    default: true,
  },
  // 分页相关
  showPagination: {
    type: Boolean,
    default: true,
  },
  pageSize: {
    type: Number,
    default: 10,
  },
  currentPage: {
    type: Number,
    default: 1,
  },
  total: {
    type: Number,
    default: 0,
  },
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100],
  },
  paginationLayout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper',
  },
  highlightCurrentRow: {
    type: Boolean,
    default: false,
  },
})

// Emits
const emit = defineEmits([
  'selection-change',
  'row-click',
  'link-click',
  'update:currentPage',
  'update:pageSize',
  'page-change',
  'size-change',
  'sort-change',
])

// 分页状态
const pagination = reactive({
  currentPage: props.currentPage,
  pageSize: props.pageSize,
  total: props.total,
})

// 表格容器引用
const tableContainerRef = ref()
// 计算出的表格高度
const computedTableHeight = ref('auto')

// 计算表格高度
const calculateTableHeight = () => {
  if (!props.autoHeight || !tableContainerRef.value) {
    return
  }

  nextTick(() => {
    const container = tableContainerRef.value
    const containerHeight = container.offsetHeight
    const paginationHeight = props.showPagination ? 72 : 0 // 分页器高度
    const padding = 20 // 减少内边距

    const availableHeight = containerHeight - paginationHeight - padding
    computedTableHeight.value = Math.max(availableHeight, 300) // 增加最小高度到300px
  })
}

// 获取实际的表格高度
const actualTableHeight = computed(() => {
  if (props.tableHeight && props.tableHeight !== 'auto') {
    return props.tableHeight
  }

  // 如果启用自适应高度，使用 CSS 来处理，而不是 JS 计算
  if (props.autoHeight) {
    return undefined // 让 CSS flexbox 自动处理
  }

  // 默认返回一个合理的高度
  return '500px'
})

// 监听 props 变化
watch(
  () => props.currentPage,
  (val) => {
    pagination.currentPage = val
  },
)

watch(
  () => props.pageSize,
  (val) => {
    pagination.pageSize = val
  },
)

watch(
  () => props.total,
  (val) => {
    pagination.total = val
  },
)

// 当前页数据（如果启用分页）
const currentPageData = computed(() => {
  if (!props.showPagination) {
    return props.data
  }

  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return props.data.slice(start, end)
})

// 行样式
const rowStyle = computed(() => {
  return props.rowClickable ? { cursor: 'pointer' } : {}
})

// 获取列值
const getColumnValue = (row, dataIndex) => {
  if (!dataIndex) return ''
  return dataIndex.split('.').reduce((obj, key) => obj?.[key], row) || ''
}

// 格式化值
const formatValue = (value, column) => {
  if (column.formatter && typeof column.formatter === 'function') {
    return column.formatter(value)
  }
  return value
}

// 高亮文本
const highlightText = (text) => {
  if (!props.highlightKeyword || !text) return text
  const regex = new RegExp(`(${props.highlightKeyword})`, 'gi')
  return text.replace(regex, '<span class="keyword-highlight">$1</span>')
}

// 获取标签类型
const getTagType = (value, tagMap) => {
  if (!tagMap) return 'info'
  return tagMap[value]?.type || 'info'
}

// 获取标签文本
const getTagText = (value, tagMap) => {
  if (!tagMap) return value
  return tagMap[value]?.text || value
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  emit('selection-change', selection)
}

// 处理行点击
const handleRowClick = (row, column, event) => {
  if (props.rowClickable) {
    emit('row-click', row, column, event)
  }
}

// 处理链接点击
const handleLinkClick = (row, column) => {
  emit('link-click', row, column)
}

// 处理排序变化
const handleSortChange = ({ column, prop, order }) => {
  emit('sort-change', { column, prop, order })
}

// 分页事件处理
const handleCurrentChange = (page) => {
  pagination.currentPage = page
  emit('update:currentPage', page)
  emit('page-change', page, pagination.pageSize)
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.currentPage = 1 // 重置到第一页
  emit('update:pageSize', size)
  emit('update:currentPage', 1)
  emit('size-change', size, 1)
}

// 监听窗口大小变化，重新计算表格高度
const handleResize = () => {
  calculateTableHeight()
}

// 生命周期钩子
onMounted(() => {
  calculateTableHeight()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.data-table-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 400px; /* 设置最小高度 */
  flex: 1;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  border: 1px solid #f0f0f0;
}

.table-wrapper {
  flex: 1;
  min-height: 300px; /* 设置最小高度 */
  overflow: hidden;
  padding: 16px 16px 0 16px; /* 给表格内容添加内边距 */
  display: flex;
  flex-direction: column;
}

.table-pagination {
  padding: 16px; /* 分页器的内边距 */
  border-top: 1px solid #f0f0f0;
  min-height: 56px;
  flex-shrink: 0;
  margin-top: auto; /* 确保分页器在底部 */
}

.pagination-controls {
  width: 100%;
}

/* 确保 Element Plus 表格能够正确填充空间 */
:deep(.el-table) {
  flex: 1;
  height: auto !important;
}

:deep(.el-table .el-table__body-wrapper) {
  max-height: none !important;
  overflow-y: auto;
}

/* 当启用自适应高度时的特殊样式 */
.table-wrapper:has(.el-table[height]) :deep(.el-table) {
  height: 100% !important;
}

/* 高亮关键词样式 */
:deep(.keyword-highlight) {
  color: #ff4d4f;
  font-weight: 600;
  background-color: transparent;
}

/* 表格行悬停效果 */
:deep(.el-table__row) {
  &:hover {
    background-color: #f5f7fa !important;
  }
}

/* 分页组件样式优化 */
:deep(.el-pagination) {
  .el-pagination__total {
    margin-right: 16px;
    font-weight: 500;
  }

  .el-pagination__sizes {
    margin-right: 16px;
  }

  .el-pagination__jump {
    margin-left: 16px;
  }

  .btn-prev,
  .btn-next {
    border-radius: 4px;
  }

  .el-pager li {
    border-radius: 4px;
    margin: 0 2px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-pagination {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .pagination-info {
    order: 2;
  }

  .pagination-controls {
    order: 1;
    width: 100%;
    justify-content: center;
  }
}
</style>
