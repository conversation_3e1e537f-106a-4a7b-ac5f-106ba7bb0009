<template>
  <div class="simple-layout-test">
    <h2>简单布局测试</h2>
    
    <!-- 搜索区域 -->
    <div class="search-area">
      <YdSearchForm
        v-model="searchForm"
        :fields="searchFields"
        :show-collapse-button="true"
        :default-collapsed="false"
        :collapsed-rows="1"
        @search="handleSearch"
        @reset="handleReset"
        @collapse-change="handleSearchCollapseChange"
      />
    </div>

    <!-- 表格区域 -->
    <div class="table-area">
      <YdDataTable
        :show-selection="true"
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :total="total"
        :current-page="currentPage"
        :page-size="pageSize"
        :auto-height="false"
        table-height="400px"
        highlight-current-row
        :row-clickable="true"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
        @sort-change="handleSortChange"
        @row-click="handleRowClick"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import YdSearchForm from '@/components/common/YdSearchForm.vue'
import YdDataTable from '@/components/common/YdDataTable.vue'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'SimpleLayoutTest',
})

// 搜索表单数据
const searchForm = ref({})

// 搜索字段配置
const searchFields = [
  {
    type: 'text',
    name: 'keyword',
    label: '关键词',
    placeholder: '请输入关键词搜索',
  },
  {
    type: 'text',
    name: 'title',
    label: '标题',
    placeholder: '请输入标题',
  },
  {
    type: 'select',
    name: 'category',
    label: '分类',
    placeholder: '请选择分类',
    options: [
      { label: '全部', value: '' },
      { label: '分类1', value: 'cat1' },
      { label: '分类2', value: 'cat2' },
      { label: '分类3', value: 'cat3' },
    ],
  },
]

// 表格列配置
const columns = [
  { dataIndex: 'id', title: 'ID', width: 80 },
  { dataIndex: 'title', title: '标题', minWidth: 200 },
  { dataIndex: 'category', title: '分类', width: 120 },
  { dataIndex: 'status', title: '状态', width: 100 },
  {
    dataIndex: 'createTime',
    title: '创建时间',
    width: 180,
    sortable: 'custom',
  },
]

// 表格数据
const tableData = ref([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 模拟数据
const mockData = Array.from({ length: 50 }, (_, index) => ({
  id: index + 1,
  title: `测试数据 ${index + 1}`,
  category: ['分类1', '分类2', '分类3'][index % 3],
  status: index % 2 === 0 ? '启用' : '禁用',
  createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleString(),
}))

// 搜索
const handleSearch = (formData) => {
  ElMessage.success(`搜索条件: ${JSON.stringify(formData)}`)
  currentPage.value = 1
  loadData()
}

// 重置
const handleReset = (formData) => {
  ElMessage.info('重置搜索条件')
  currentPage.value = 1
  loadData()
}

// 分页变化
const handlePageChange = (page) => {
  currentPage.value = page
  loadData()
}

// 页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadData()
}

// 排序变化
const handleSortChange = ({ prop, order }) => {
  ElMessage.info(`排序: ${prop} - ${order}`)
  loadData()
}

// 行点击
const handleRowClick = (row) => {
  ElMessage.info(`点击了行: ${row.title}`)
}

// 处理搜索区域展开收起变化
const handleSearchCollapseChange = (collapsed) => {
  ElMessage.success(`搜索区域${collapsed ? '收起' : '展开'}`)
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 500))
    
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    tableData.value = mockData.slice(start, end)
    total.value = mockData.length
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.simple-layout-test {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-area {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.table-area {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}
</style>
