/**
 * 获取元素可见信息
 * @param {HTMLElement} element
 * @returns {VisibleDomRect}
 */
export function getElementVisibleRect(element) {
  if (!element) {
    return {
      bottom: 0,
      height: 0,
      left: 0,
      right: 0,
      top: 0,
      width: 0,
    }
  }

  const rect = element.getBoundingClientRect()
  const viewHeight = Math.max(
    document.documentElement.clientHeight,
    window.innerHeight,
  )

  const top = Math.max(rect.top, 0)
  const bottom = Math.min(rect.bottom, viewHeight)

  const viewWidth = Math.max(
    document.documentElement.clientWidth,
    window.innerWidth,
  )

  const left = Math.max(rect.left, 0)
  const right = Math.min(rect.right, viewWidth)

  return {
    bottom,
    height: Math.max(0, bottom - top),
    left,
    right,
    top,
    width: Math.max(0, right - left),
  }
}

/**
 * 触发窗口resize事件
 */
export function triggerWindowResize() {
  const event = new Event('resize')
  window.dispatchEvent(event)
}
