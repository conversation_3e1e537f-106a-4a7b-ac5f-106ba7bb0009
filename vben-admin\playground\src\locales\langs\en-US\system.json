{"title": "System Management", "dept": {"name": "Department", "title": "Department Management", "deptName": "Department Name", "status": "Status", "createTime": "Create Time", "remark": "Remark", "operation": "Operation", "parentDept": "Parent Department"}, "menu": {"title": "Menu Management", "parent": "<PERSON><PERSON>", "menuTitle": "Title", "menuName": "<PERSON>u Name", "name": "<PERSON><PERSON>", "type": "Type", "typeCatalog": "Catalog", "typeMenu": "<PERSON><PERSON>", "typeButton": "<PERSON><PERSON>", "typeLink": "Link", "typeEmbedded": "Embedded", "icon": "Icon", "activeIcon": "Active Icon", "activePath": "Active Path", "path": "Route Path", "component": "Component", "status": "Status", "authCode": "Auth Code", "badge": "Badge", "operation": "Operation", "linkSrc": "Link Address", "affixTab": "Affix In Tabs", "keepAlive": "Keep Alive", "hideInMenu": "Hide In Menu", "hideInTab": "Hide In Tabbar", "hideChildrenInMenu": "Hide Children In Menu", "hideInBreadcrumb": "Hide In Breadcrumb", "advancedSettings": "Other Settings", "activePathMustExist": "The path could not find a valid menu", "activePathHelp": "When jumping to the current route, \nthe menu path that needs to be activated must be specified when it does not display in the navigation menu.", "badgeType": {"title": "Badge Type", "dot": "Dot", "normal": "Text", "none": "None"}, "badgeVariants": "Badge Style"}, "role": {"title": "Role Management", "list": "Role List", "name": "Role", "roleName": "Role Name", "id": "Role ID", "status": "Status", "remark": "Remark", "createTime": "Creation Time", "operation": "Operation", "permissions": "Permissions", "setPermissions": "Permissions"}}