<template>
  <div class="vben-page-layout">
    <!-- 搜索表单区域 -->
    <div v-if="$slots.search" class="search-section" :class="{ 'collapsed': searchCollapsed }">
      <slot name="search" :on-collapse-change="handleSearchCollapseChange" />
    </div>
    
    <!-- 内容区域 -->
    <div class="content-section" :style="contentStyle">
      <slot />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'

defineOptions({
  name: 'VbenPageLayout',
})

// Props
const props = defineProps({
  // 搜索区域的高度（展开时）
  searchHeight: {
    type: Number,
    default: 120, // 默认高度，可以根据实际情况调整
  },
  // 搜索区域收起时的高度
  collapsedSearchHeight: {
    type: Number,
    default: 80, // 收起时的高度
  },
})

// Emits
const emit = defineEmits(['search-collapse-change'])

// 搜索区域是否收起
const searchCollapsed = ref(false)
// 搜索区域实际高度
const actualSearchHeight = ref(props.searchHeight)

// 计算内容区域的样式
const contentStyle = computed(() => {
  return {
    height: `calc(100% - ${actualSearchHeight.value + 16}px)`, // 16px 是搜索区域的 margin-bottom
  }
})

// 处理搜索区域展开收起变化
const handleSearchCollapseChange = (collapsed) => {
  searchCollapsed.value = collapsed
  actualSearchHeight.value = collapsed 
    ? props.collapsedSearchHeight 
    : props.searchHeight
    
  emit('search-collapse-change', collapsed)
  
  // 等待 DOM 更新后触发窗口 resize 事件，让表格重新计算高度
  nextTick(() => {
    window.dispatchEvent(new Event('resize'))
  })
}

// 监听搜索区域高度变化
const updateSearchHeight = () => {
  const searchElement = document.querySelector('.search-section')
  if (searchElement) {
    const height = searchElement.offsetHeight
    if (height > 0) {
      actualSearchHeight.value = height
    }
  }
}

// 使用 ResizeObserver 监听搜索区域高度变化
let resizeObserver = null

onMounted(() => {
  nextTick(() => {
    updateSearchHeight()
    
    // 监听搜索区域高度变化
    const searchElement = document.querySelector('.search-section')
    if (searchElement && window.ResizeObserver) {
      resizeObserver = new ResizeObserver(() => {
        updateSearchHeight()
      })
      resizeObserver.observe(searchElement)
    }
  })
})

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})

// 暴露方法
defineExpose({
  searchCollapsed,
  handleSearchCollapseChange,
  actualSearchHeight,
})
</script>

<style scoped>
.vben-page-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
  overflow: hidden;
}

.search-section {
  flex-shrink: 0;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.search-section.collapsed {
  /* 收起状态的样式可以在这里添加 */
}

.content-section {
  flex: 1;
  min-height: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
</style>
