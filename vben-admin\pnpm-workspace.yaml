packages:
  - internal/*
  - internal/lint-configs/*
  - packages/*
  - packages/@core/base/*
  - packages/@core/ui-kit/*
  - packages/@core/forward/*
  - packages/@core/*
  - packages/effects/*
  - packages/business/*
  - apps/*
  - scripts/*
  - docs
  - playground

catalog:
  '@ast-grep/napi': ^0.37.0
  '@changesets/changelog-github': ^0.5.1
  '@changesets/cli': ^2.29.5
  '@changesets/git': ^3.0.4
  '@clack/prompts': ^0.10.1
  '@commitlint/cli': ^19.8.1
  '@commitlint/config-conventional': ^19.8.1
  '@ctrl/tinycolor': ^4.1.0
  '@eslint/js': ^9.30.1
  '@faker-js/faker': ^9.9.0
  '@iconify/json': ^2.2.354
  '@iconify/tailwind': ^1.2.0
  '@iconify/vue': ^5.0.0
  '@intlify/core-base': ^11.1.7
  '@intlify/unplugin-vue-i18n': ^6.0.8
  '@jspm/generator': ^2.6.2
  '@manypkg/get-packages': ^3.0.0
  '@nolebase/vitepress-plugin-git-changelog': ^2.18.0
  '@playwright/test': ^1.53.2
  '@pnpm/workspace.read-manifest': ^1000.2.0
  '@stylistic/stylelint-plugin': ^3.1.3
  '@tailwindcss/nesting': 0.0.0-insiders.565cd3e
  '@tailwindcss/typography': ^0.5.16
  '@tanstack/vue-query': ^5.81.5
  '@tanstack/vue-store': ^0.7.1
  '@types/archiver': ^6.0.3
  '@types/eslint': ^9.6.1
  '@types/html-minifier-terser': ^7.0.2
  '@types/json-bigint': ^1.0.4
  '@types/jsonwebtoken': ^9.0.10
  '@types/lodash.clonedeep': ^4.5.9
  '@types/lodash.get': ^4.4.9
  '@types/lodash.isequal': ^4.5.8
  '@types/lodash.set': ^4.3.9
  '@types/node': ^22.16.0
  '@types/nprogress': ^0.2.3
  '@types/postcss-import': ^14.0.3
  '@types/qrcode': ^1.5.5
  '@types/qs': ^6.14.0
  '@types/sortablejs': ^1.15.8
  '@typescript-eslint/eslint-plugin': ^8.35.1
  '@typescript-eslint/parser': ^8.35.1
  '@vee-validate/zod': ^4.15.1
  '@vite-pwa/vitepress': ^1.0.0
  '@vitejs/plugin-vue': ^5.2.4
  '@vitejs/plugin-vue-jsx': ^4.2.0
  '@vue/reactivity': ^3.5.17
  '@vue/shared': ^3.5.17
  '@vue/test-utils': ^2.4.6
  '@vueuse/core': ^13.4.0
  '@vueuse/integrations': ^13.4.0
  '@vueuse/motion': ^3.0.3
  ant-design-vue: ^4.2.6
  archiver: ^7.0.1
  autoprefixer: ^10.4.21
  axios: ^1.10.0
  axios-mock-adapter: ^2.1.0
  cac: ^6.7.14
  chalk: ^5.4.1
  cheerio: ^1.1.0
  circular-dependency-scanner: ^2.3.0
  class-variance-authority: ^0.7.1
  clsx: ^2.1.1
  commitlint-plugin-function-rules: ^4.0.2
  consola: ^3.4.2
  cross-env: ^7.0.3
  cspell: ^8.19.4
  cssnano: ^7.0.7
  cz-git: ^1.11.2
  czg: ^1.11.1
  dayjs: ^1.11.13
  defu: ^6.1.4
  depcheck: ^1.4.7
  dotenv: ^16.6.1
  echarts: ^5.6.0
  element-plus: ^2.10.2
  eslint: ^9.30.1
  eslint-config-turbo: ^2.5.4
  eslint-plugin-command: ^3.3.1
  eslint-plugin-eslint-comments: ^3.2.0
  eslint-plugin-import-x: ^4.16.1
  eslint-plugin-jsdoc: ^50.8.0
  eslint-plugin-jsonc: ^2.20.1
  eslint-plugin-n: ^17.20.0
  eslint-plugin-no-only-tests: ^3.3.0
  eslint-plugin-perfectionist: ^4.15.0
  eslint-plugin-prettier: ^5.5.1
  eslint-plugin-regexp: ^2.9.0
  eslint-plugin-unicorn: ^59.0.1
  eslint-plugin-unused-imports: ^4.1.4
  eslint-plugin-vitest: ^0.5.4
  eslint-plugin-vue: ^10.2.0
  execa: ^9.6.0
  find-up: ^7.0.0
  get-port: ^7.1.0
  globals: ^16.3.0
  h3: ^1.15.3
  happy-dom: ^17.6.3
  html-minifier-terser: ^7.2.0
  is-ci: ^4.1.0
  json-bigint: ^1.0.0
  jsonc-eslint-parser: ^2.4.0
  jsonwebtoken: ^9.0.2
  lefthook: ^1.11.14
  lodash.clonedeep: ^4.5.0
  lodash.get: ^4.4.2
  lodash.isequal: ^4.5.0
  lodash.set: ^4.3.2
  lucide-vue-next: ^0.507.0
  medium-zoom: ^1.1.0
  naive-ui: ^2.42.0
  nitropack: ^2.11.13
  nprogress: ^0.2.0
  ora: ^8.2.0
  pinia: ^3.0.3
  pinia-plugin-persistedstate: ^4.4.1
  pkg-types: ^2.2.0
  playwright: ^1.53.2
  postcss: ^8.5.6
  postcss-antd-fixes: ^0.2.0
  postcss-html: ^1.8.0
  postcss-import: ^16.1.1
  postcss-preset-env: ^10.2.4
  postcss-scss: ^4.0.9
  prettier: ^3.6.2
  prettier-plugin-tailwindcss: ^0.6.13
  publint: ^0.3.12
  qrcode: ^1.5.4
  qs: ^6.14.0
  radix-vue: ^1.9.17
  resolve.exports: ^2.0.3
  rimraf: ^6.0.1
  rollup: ^4.44.1
  rollup-plugin-visualizer: ^5.14.0
  sass: ^1.89.2
  secure-ls: ^2.0.0
  sortablejs: ^1.15.6
  stylelint: ^16.21.0
  stylelint-config-recess-order: ^6.1.0
  stylelint-config-recommended: ^16.0.0
  stylelint-config-recommended-scss: ^14.1.0
  stylelint-config-recommended-vue: ^1.6.1
  stylelint-config-standard: ^38.0.0
  stylelint-order: ^7.0.0
  stylelint-prettier: ^5.0.3
  stylelint-scss: ^6.12.1
  tailwind-merge: ^2.6.0
  tailwindcss: ^3.4.17
  tailwindcss-animate: ^1.0.7
  theme-colors: ^0.1.0
  tippy.js: ^6.3.7
  turbo: ^2.5.4
  typescript: ^5.8.3
  unbuild: ^3.5.0
  unplugin-element-plus: ^0.10.0
  vee-validate: ^4.15.1
  vite: ^6.3.5
  vite-plugin-compression: ^0.5.1
  vite-plugin-dts: ^4.5.4
  vite-plugin-html: ^3.2.2
  vite-plugin-lazy-import: ^1.0.7
  vite-plugin-pwa: ^1.0.1
  vite-plugin-vue-devtools: ^7.7.7
  vitepress: ^1.6.3
  vitepress-plugin-group-icons: ^1.6.1
  vitest: ^3.2.4
  vue: ^3.5.17
  vue-eslint-parser: ^10.2.0
  vue-i18n: ^11.1.7
  vue-json-viewer: ^3.0.4
  vue-router: ^4.5.1
  vue-tippy: ^6.7.1
  vue-tsc: 2.2.10
  vxe-pc-ui: ^4.7.12
  vxe-table: ^4.14.4
  watermark-js-plus: ^1.6.2
  zod: ^3.25.67
  zod-defaults: ^0.1.3
