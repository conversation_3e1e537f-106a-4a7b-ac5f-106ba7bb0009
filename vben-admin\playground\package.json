{"name": "@vben/playground", "version": "5.5.7", "homepage": "https://vben.pro", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "playground"}, "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "type": "module", "scripts": {"build": "pnpm vite build --mode production", "build:analyze": "pnpm vite build --mode analyze", "dev": "pnpm vite --mode development", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "test:e2e": "playwright test", "test:e2e-ui": "playwright test --ui", "test:e2e-codegen": "playwright codegen"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@tanstack/vue-query": "catalog:", "@vben-core/menu-ui": "workspace:*", "@vben/access": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/layouts": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/preferences": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/styles": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "ant-design-vue": "catalog:", "dayjs": "catalog:", "json-bigint": "catalog:", "pinia": "catalog:", "vue": "catalog:", "vue-router": "catalog:"}, "devDependencies": {"@types/json-bigint": "catalog:"}}