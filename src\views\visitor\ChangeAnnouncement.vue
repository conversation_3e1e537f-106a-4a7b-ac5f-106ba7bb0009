<template>
  <VbenPageLayout @search-collapse-change="handleSearchCollapseChange">
    <!-- 搜索表单插槽 -->
    <template #search>
      <YdSearchForm
        v-model="searchForm"
        :fields="searchFields"
        :show-collapse-button="true"
        :default-collapsed="false"
        :collapsed-rows="1"
        @search="handleSearch"
        @reset="handleReset"
        @collapse-change="handleSearchCollapseChange"
      />
    </template>

    <!-- 数据表格 -->
    <YdDataTable
      :show-selection="true"
      :columns="columns"
      :data="tableData"
      :loading="loading"
      :total="total"
      :current-page="currentPage"
      :page-size="pageSize"
      :auto-height="true"
      highlight-current-row
      :row-clickable="true"
      @page-change="handlePageChange"
      @size-change="handleSizeChange"
      @sort-change="handleSortChange"
      @row-click="handleRowClick"
    />
  </VbenPageLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import VbenPageLayout from '@/components/common/VbenPageLayout.vue'
import YdSearchForm from '@/components/common/YdSearchForm.vue'
import YdDataTable from '@/components/common/YdDataTable.vue'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'ChangeAnnouncement',
})

// 搜索表单数据
const searchForm = ref({})

// 搜索字段配置
const searchFields = [
  {
    type: 'text',
    name: 'announcementNumber',
    label: '公告编号',
    placeholder: '请输入公告编号',
  },
  {
    type: 'text',
    name: 'title',
    label: '公告标题',
    placeholder: '请输入公告标题',
  },
  {
    type: 'daterange',
    name: 'publishTime',
    label: '发布时间',
  },
  {
    type: 'select',
    name: 'procurementMethod',
    label: '采购方式',
    placeholder: '请选择采购方式',
    options: [
      { label: '全部', value: '' },
      { label: '货物', value: 'goods' },
      { label: '服务', value: 'service' },
      { label: '工程', value: 'project' },
    ],
  },
  {
    type: 'select',
    name: 'status',
    label: '状态',
    placeholder: '请选择状态',
    options: [
      { label: '全部', value: '' },
      { label: '正在报名', value: 'registering' },
      { label: '已截止', value: 'expired' },
      { label: '已暂停', value: 'paused' },
    ],
  },
  {
    type: 'text',
    name: 'purchaser',
    label: '采购单位',
    placeholder: '请输入采购单位',
  },
  {
    type: 'select',
    name: 'changeType',
    label: '变更类型',
    placeholder: '请选择变更类型',
    options: [
      { label: '全部', value: '' },
      { label: '时间变更', value: 'time' },
      { label: '内容变更', value: 'content' },
      { label: '取消变更', value: 'cancel' },
    ],
  },
]

// 表格列配置
const columns = [
  { dataIndex: 'serialNumber', title: '序号', width: 80 },
  { dataIndex: 'announcementNumber', title: '公告编号', width: 120 },
  { dataIndex: 'title', title: '公告标题', minWidth: 200 },
  { dataIndex: 'purchaser', title: '采购单位', width: 150 },
  { dataIndex: 'type', title: '采购类型', width: 100 },
  {
    dataIndex: 'publishTime',
    title: '发布时间',
    width: 150,
    sortable: 'custom',
  },
  {
    dataIndex: 'registrationDeadline',
    title: '报名截止时间',
    width: 150,
    sortable: 'custom',
  },
  { dataIndex: 'status', title: '状态', width: 80 },
]

// 表格数据
const tableData = ref([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 排序状态
const sortInfo = ref({
  prop: '',
  order: '',
})

// 路由实例
const router = useRouter()

// 模拟数据
const mockData = [
  {
    id: 1,
    serialNumber: 1,
    announcementNumber: '**********',
    title: '康复治疗中心',
    purchaser: 'XXXX医疗机构',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-11-07',
    status: '正在报名',
  },
  {
    id: 2,
    serialNumber: 2,
    announcementNumber: '**********',
    title: '巴马瑶族自治县医疗设备治疗仪采购',
    purchaser: 'XXXXX民医院',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-11-07',
    status: '正在报名',
  },
  {
    id: 3,
    serialNumber: 3,
    announcementNumber: '**********',
    title: '康复治疗中心设备采购变更公告',
    purchaser: 'XXXX医疗机构',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-11-07',
    status: '正在报名',
  },
  {
    id: 4,
    serialNumber: 4,
    announcementNumber: '**********',
    title: '分析检查子平台16012022324',
    purchaser: 'XXXXX民医院',
    type: '服务',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-10-30',
    status: '正在报名',
  },
  {
    id: 5,
    serialNumber: 5,
    announcementNumber: '**********',
    title: '学习与康复设备采购子科室物品...',
    purchaser: 'XXXX医疗机构',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-11-07',
    status: '正在报名',
  },
  {
    id: 6,
    serialNumber: 6,
    announcementNumber: '**********',
    title: '康复治疗中心',
    purchaser: 'XXXXX民医院',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-11-07',
    status: '正在报名',
  },
  {
    id: 7,
    serialNumber: 7,
    announcementNumber: '**********',
    title: '巴马瑶族自治县医疗设备治疗仪采购',
    purchaser: 'XXXX医疗机构',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-11-07',
    status: '正在报名',
  },
  {
    id: 8,
    serialNumber: 8,
    announcementNumber: '**********',
    title: '康复治疗中心设备采购变更公告',
    purchaser: 'XXXXX民医院',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-12-07',
    status: '正在报名',
  },
  {
    id: 9,
    serialNumber: 9,
    announcementNumber: '**********',
    title: '分析检查子平台16012022324',
    purchaser: 'XXXX医疗机构',
    type: '服务',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-07-07',
    status: '报名截止',
  },
  {
    id: 10,
    serialNumber: 10,
    announcementNumber: '**********',
    title: '学习与康复设备采购子科室物品...',
    purchaser: 'XXXXX民医院',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-07-07',
    status: '报名截止',
  },
  {
    id: 10,
    serialNumber: 10,
    announcementNumber: '**********',
    title: '学习与康复设备采购子科室物品...',
    purchaser: 'XXXXX民医院',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-07-07',
    status: '报名截止',
  },
  {
    id: 10,
    serialNumber: 10,
    announcementNumber: '**********',
    title: '学习与康复设备采购子科室物品...',
    purchaser: 'XXXXX民医院',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-07-07',
    status: '报名截止',
  },
  {
    id: 10,
    serialNumber: 10,
    announcementNumber: '**********',
    title: '学习与康复设备采购子科室物品...',
    purchaser: 'XXXXX民医院',
    type: '货物',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-07-07',
    status: '报名截止',
  },
]

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadData()
}

// 重置
const handleReset = () => {
  currentPage.value = 1
  loadData()
}

// 分页变化
const handlePageChange = (page) => {
  currentPage.value = page
  loadData()
}

// 页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadData()
}

// 排序变化
const handleSortChange = ({ prop, order }) => {
  sortInfo.value.prop = prop
  sortInfo.value.order = order
  currentPage.value = 1
  loadData()
}

// 行点击
const handleRowClick = (row) => {
  if (row && row.id) {
    router.push(`/announcement/${row.id}`)
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 500))

    let data = [...mockData]

    // 应用排序
    if (sortInfo.value.prop && sortInfo.value.order) {
      data.sort((a, b) => {
        const aValue = a[sortInfo.value.prop]
        const bValue = b[sortInfo.value.prop]

        // 日期排序
        if (
          sortInfo.value.prop === 'publishTime' ||
          sortInfo.value.prop === 'registrationDeadline'
        ) {
          const dateA = new Date(aValue)
          const dateB = new Date(bValue)

          if (sortInfo.value.order === 'ascending') {
            return dateA - dateB
          } else {
            return dateB - dateA
          }
        }

        // 其他类型排序
        if (sortInfo.value.order === 'ascending') {
          return aValue > bValue ? 1 : -1
        } else {
          return aValue < bValue ? 1 : -1
        }
      })
    }

    tableData.value = data
    total.value = data.length
  } catch (error) {
    ElMessage.error(error)
  } finally {
    loading.value = false
  }
}

// 处理搜索区域展开收起变化
const handleSearchCollapseChange = (collapsed) => {
  console.log('搜索区域收起状态:', collapsed)
  // 这里可以添加其他需要在搜索区域展开收起时执行的逻辑
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
/* 使用 VbenPageLayout 后，不需要额外的样式 */
</style>
